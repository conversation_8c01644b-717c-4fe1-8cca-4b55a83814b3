providers_CRUDMLSServer_RDS_S3.py:
lines: 167, 197, 208, 

validations.py:
lines: 599, 611, 1002, 

database.py:
lines: 




**Objective:** Refactor the `mls_configs` entity within `providers_CRUDMLSServer_RDS_S3.py` to match the new database schema. and change 'validations.py' for new fields.

**File to Modify:** `providers_CRUDMLSServer_RDS_S3.py`, 'validations.py'

**Detailed Instructions for Modifying `entity_lookup_data[MLS_CONFIG]`:**

1.  **Fields for Removal:**
    * fields to remove: `name`, `resource`, `query`, `limit`, `unique_field`, `update_interval`, `photo_time_stamp_field`, ``, ``, ``, ``, ``, ``.
    * Remove these from `ACCEPTABLE_INPUT_FIELDS` and `LIST_FILTER_ACCEPTABLE_FIELDS_KEY` for `MLS_CONFIG`.

2.  **Fields for Addition:**
    * Add the following new fields to `ACCEPTABLE_INPUT_FIELDS` and `LIST_FILTER_ACCEPTABLE_FIELDS_KEY` for `MLS_CONFIG`. Ensure their types and constraints (nullable, FK, choices) are reflected in the validator and database considerations:
        * `filter_param` (varchar, nullable)
        * `path_param` (varchar, nullable)
        * `custom_prefix_key` (varchar, nullable)
        * `custom_prefix_value` (varchar, nullable)
        * `custom_mapping` (varchar, nullable)
        * `timezone` (varchar, nullable)

**Update Validator (`validations.py`):**

* Modify the `validations.py` function (referenced by `INPUT_VALIDATOR_FUNCTION` for `MLS_CONFIG`).
* Remove validation logic for all old removed fields.
* Implement validation for all new fields, including their data types, nullability

**Database Schema and Conflict Considerations (`mls_configs` table):**

* ** consider that the code runs every time webpage is refreshed, so removing the old fields will not cause any issues. or should it run only once through sql elsewhere?
* **Analyze and outline the necessary SQL `ALTER TABLE mls_configs` statements.** This should include:
    * `DROP COLUMN` for each removed field.
    * `ADD COLUMN` for each new field, specifying its PostgreSQL data type (e.g., `VARCHAR(255)`, `INTEGER`, `TIMESTAMP`, `TEXT`, `BOOLEAN`, potentially `your_enum_type` for choices if enums are used) and constraints (`NULL`/`NOT NULL`, `DEFAULT`).
* **Data Integrity and Loss:**
    * Acknowledge that data in removed columns will be lost.
    * the old data is not needed so no migration is needed.
* **Indexes:** Recommend creating indexes on new fields that will be frequently used in `WHERE` clauses (filters) or `JOIN` conditions.
* **Atomicity:** Advise that database schema changes should be applied carefully, ideally within a transaction if multiple DDL statements are involved.

**Code-Wide Impact Analysis:**

* Identify and list other sections of `providers_CRUDMLSServer_RDS_S3.py` (e.g., within the main `lambda_handler` logic for `MLS_CONFIG`, or any helper functions) that currently reference the old `mls_configs` fields.
* These sections must be updated to use the new field names and data structure.
* Ensure that data passed to `RDSHandler` methods for `MLS_CONFIG` reflects the new schema.


**Final Check:**
* The primary source of truth for field names, types, and constraints is the provided image.
* Ensure all changes are consistent with the existing coding patterns and practices in the file.