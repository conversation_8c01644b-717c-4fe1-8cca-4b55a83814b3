import logging
from typing import Literal

from pydantic import (
    BaseModel as BM,
    ValidationError,
    field_validator,
    Field,
)

from realtyfeed.exceptions import (
    RealtyfeedException,
    DatabaseException,
    DataInvalidException,
)
from realtyfeed.orm import RDSHandler


"""
Data validation module for MLS Server API.

This module provides validation functions and Pydantic models for validating
request data for MLS Server API endpoints. It ensures that all incoming data
meets the required format and constraints before processing.

Key components:
- BaseModel: Extended Pydantic model with custom error handling
- GroupMLSDocumentQueryParams: Model for validating GET request query parameters
- GroupMLSDocumentCreateBody: Model for validating POST request body
- GroupMLSDocumentUpdateBody: Model for validating PUT request body
- GroupMLSDocumentDELETEBody: Model for validating DELETE request body
- validate_data: Function to validate request data based on entity type and HTTP method
- Entity-specific validators (mls_data_validator, mls_document_data_validator, etc.)

The validation includes checking field types, required fields, field lengths,
and allowed values for enumerated fields.
"""



logger = logging.getLogger()  # Initialize logger for this module


class BaseModel(BM):
    def __init__(self, *args, **kwargs):
        try:
            super().__init__(**kwargs)  # Initialize the parent Pydantic BaseModel with provided kwargs
        except ValidationError as err:
            logger.info(err)  # Log the validation error for debugging
            error_message = list()  # Initialize empty list to collect formatted error messages
            for error in err.errors():  # Iterate through each validation error
                msg = error["msg"]  # Extract the error message
                loc = error.get("loc", [])  # Get the location (field path) where the error occurred
                loc = ".".join([el for el in loc if isinstance(el, str)])  # Join location parts into dot-notation path
                error_message.append(f"{loc} {msg}")  # Format error as "field_path error_message"
            error_message_str = ", ".join(error_message)  # Join all errors with commas
            raise RealtyfeedException(error_message=error_message_str)  # Raise custom exception with formatted errors


logger = logging.getLogger()  # Duplicate logger initialization (likely a mistake)


TABLE = "table_key"  # Constant for accessing table name in entity lookup dictionaries
INPUT_VALIDATOR_FUNCTION = "input_validator_function_holder_key"  # Constant for accessing validator function
ACCEPTABLE_INPUT_FIELDS = "acceptable_input_fields_key"  # Constant for accessing allowed input fields
LIST_FILTER_ACCEPTABLE_FIELDS_KEY = "list_filter_acceptable_fields_key"  # Constant for accessing allowed filter fields
# Error prefix code
ERROR_CODE_PREFIX = "prv-05-01"  # Prefix for error codes in this module


class GroupMLSDocumentQueryParams(BaseModel):
    id: int | None = None  # Optional document ID for filtering
    mls_vendor_group_id: int  # Required vendor group ID for authorization
    name: str | None = None  # Optional document name for filtering

    # pagination_fields
    limit: int | None = 10  # Default limit of 10 results per page
    offset: int | None = 0  # Default offset of 0 (start from beginning)
    orderby_field: Literal["creation_date"] | None = "creation_date"  # Field to sort by, only creation_date allowed
    orderby_direction: Literal["DESC", "ASC"] | None = "DESC"  # Sort direction, default descending (newest first)

    @property
    def query_filter_fields(self):
        return ("id", "mls_vendor_group_id", "name")  # Fields that can be used for filtering

    @field_validator("name", mode="before")
    def change_like_filter(v: str) -> str:
        return f"%{v}%"  # Add wildcards to name for partial matching with SQL LIKE


class GroupMLSDocumentCreateBody(BaseModel):
    mls_vendor_group_id: int  # Required vendor group ID for authorization
    name: str  # Required document name
    url: str  # Required document URL (S3 path)
    document_type: str | None = None  # Optional document type

    @field_validator("url", mode="after")
    def validate_url(url: str) -> str:
        ACCEPTABLE_DOCUMENT_TYPES = ["pdf", "xls", "doc", "docx", "txt"]  # List of allowed file extensions
        file_name = url.split("/")[-1]  # Extract filename from URL
        file_extension = file_name.split(".")[-1]  # Extract file extension
        if file_extension.lower() not in ACCEPTABLE_DOCUMENT_TYPES:  # Check if extension is allowed
            raise ValueError(f"document types must be {ACCEPTABLE_DOCUMENT_TYPES}")  # Raise error if not allowed
        return url.lower()  # Return lowercase URL for consistency


class GroupMLSDocumentUpdateBody(BaseModel):
    id: int  # Required document ID to identify which document to update
    name: str | None = Field(None, max_length=200)  # Optional name with max length validation
    url: str | None = Field(None, max_length=200)  # Optional URL with max length validation
    document_type: str | None = None  # Optional document type

    @field_validator("url", mode="after")
    def validate_url(url: str) -> str:
        ACCEPTABLE_DOCUMENT_TYPES = ["pdf", "xls", "doc", "docx", "txt"]  # List of allowed file extensions
        file_name = url.split("/")[-1]  # Extract filename from URL
        file_extension = file_name.split(".")[-1]  # Extract file extension
        if file_extension.lower() not in ACCEPTABLE_DOCUMENT_TYPES:  # Check if extension is allowed
            raise ValueError(f"document types must be {ACCEPTABLE_DOCUMENT_TYPES}")  # Raise error if not allowed
        return url.lower()  # Return lowercase URL for consistency


class GroupMLSDocumentDELETEBody(BaseModel):
    id: int  # Required document ID to identify which document to delete


def validate_data(entity_lookup_data, entity, data, http_method):
    """
    This function validate all input data.
    If the http method was POST or PUT the function use entities validators to
    validate input data fields, the function get entity validators from entity_lookup_data.
    """
    logger.info(
        f"Start validate_data(entity={entity}, data={data}, http_method={http_method})..."
    )  # Log function start with parameters for debugging
    errors = []  # Initialize empty list to collect validation errors
    if not entity or entity.lower() not in entity_lookup_data.keys():
        # Validate that entity exists and is a valid entity type in our lookup data
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-0301",
                "message": f"Wrong 'entity': The request body 'entity' field must contain one of {entity_lookup_data.keys()}",
            }
        )  # Add error if entity is missing or invalid
    elif not data:
        # Validate that data is provided
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-0302",
                "message": "Missing 'data' field error: The request must have 'data' field.",
            }
        )  # Add error if data is missing
    elif http_method == "DELETE":
        # For DELETE requests, validate that an integer ID is provided
        id = data.get("id")
        if not id or not isinstance(id, int):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0303",
                    "message": "'DELETE' request error: The request data must have an integer 'id' field.",
                }
            )  # Add error if ID is missing or not an integer
    elif http_method == "GET":
        # For GET requests, validate query parameters
        id = data.get("id")
        list_filter = data.get("list_filter")

        # Checking if selected_fields are acceptable::
        # TODO: For future featurs. We can use this validator in future...
        # acceptable_selected_fields = set(
        #     entity_lookup_data[entity][ACCEPTABLE_SELECTED_FIELDS]
        # )
        # if acceptable_selected_fields != {"*"}:
        #     selected_fields = data.get("selected_fields")
        #     if selected_fields and not set(selected_fields).issubset(
        #         acceptable_selected_fields
        #     ):
        #         errors.append(
        #             {
        #                 "code": f"{ERROR_CODE_PREFIX}-0304",
        #                 "message": f"'GET' unacceptable 'selected_fields' error: Acceptable selected fields are {acceptable_selected_fields}. the request contain {set(selected_fields)-acceptable_selected_fields} fields that are not in acceptable selected fields",
        #             }
        #         )

        if id and not isinstance(id, int):
            # If ID is provided, validate it's an integer
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0305",
                    "message": "'GET' request 'id' error: The request data must have an integer 'id' field.",
                }
            )  # Add error if ID is not an integer

        elif list_filter and isinstance(list_filter, dict):
            # If list_filter is provided, validate its structure and contents
            filters = list_filter.get("filters")  # Extract filters list
            order_by = list_filter.get("order_by")  # Extract order_by field
            limit = list_filter.get("limit")  # Extract limit value
            offset = list_filter.get("offset")  # Extract offset value

            if order_by and not isinstance(order_by, str):
                # Validate order_by is a string if provided
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0306",
                        "message": "'GET' request 'list_filte'/'oreder_by' error: The 'order_by' field of 'list_filter' data must be an String.",
                    }
                )  # Add error if order_by is not a string

            if limit and not isinstance(limit, int):
                # Validate limit is an integer if provided
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0307",
                        "message": "'GET' request 'list_filte'/'limit' error: The 'limit' field of 'list_filter' data must be an Integer.",
                    }
                )  # Add error if limit is not an integer

            if offset and not isinstance(offset, int):
                # Validate offset is an integer if provided
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0308",
                        "message": "'GET' request 'list_filte'/'offset' error: The 'offset' field of 'list_filter' data must be an Integer.",
                    }
                )  # Add error if offset is not an integer

            if filters and not isinstance(filters, list):
                # Validate filters is a list if provided
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0309",
                        "message": "'GET' request 'list_filte'/'filters' error: The 'filters' field of 'list_filter' data must be one of list, json or dict.",
                    }
                )  # Add error if filters is not a list
            elif filters and isinstance(filters, list):
                # If filters is a valid list, validate the filter fields against acceptable fields
                # Geting RDS handler to using of generate_sql_condition_by_filter() method
                try:
                    rds_handler = RDSHandler(
                        table_name=entity_lookup_data[entity][TABLE],
                        is_cursor_dict=True,
                    )  # Initialize database handler for the entity's table
                except DatabaseException as e:
                    # Log database connection errors
                    logger.exception(
                        f"ERROR | validate_data() | Database connection error. | {e}"
                    )

                try:
                    # Validating filter fields using generate_sql_condition_by_filter rds method.
                    # The method raise DataInvalidException exception if input filter field not in acceptable field list.
                    acceptable_fields = entity_lookup_data[entity][
                        LIST_FILTER_ACCEPTABLE_FIELDS_KEY
                    ]  # Get list of acceptable filter fields for this entity
                    list_filter = data.get("list_filter")  # Get list_filter again (redundant)
                    filters = list_filter.get("filters", "")  # Get filters with empty string default
                    sql_condition = rds_handler.generate_sql_condition_by_filter(
                        conditions=filters, acceptable_fields=acceptable_fields
                    )  # Try to generate SQL conditions to validate filter fields
                except DataInvalidException as e:
                    # If validation fails, add error with list of acceptable fields
                    errors.append(
                        {
                            "code": f"{ERROR_CODE_PREFIX}-03091",
                            "message": f"Acceptable fields for using in filters are {entity_lookup_data[entity][LIST_FILTER_ACCEPTABLE_FIELDS_KEY]} fields.",
                        }
                    )

        elif list_filter and not isinstance(list_filter, dict):
            # Validate list_filter is a dictionary if provided
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0310",
                    "message": "'GET' request wrong 'list_filte' error: The 'list_filter' field must be an JSON.",
                }
            )  # Add error if list_filter is not a dictionary

        elif not id and not list_filter:
            # Validate that either id or list_filter is provided for GET requests
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0311",
                    "message": "'GET' empty body: The 'GET' request data must have either an Integer 'id' field or a JSON 'list_filter' field.",
                }
            )  # Add error if neither id nor list_filter is provided

    elif http_method in ["PUT", "POST"]:
        # For PUT and POST requests, validate input fields against acceptable fields
        acceptable_input_fields = set(
            entity_lookup_data[entity][ACCEPTABLE_INPUT_FIELDS]
        )  # Get set of acceptable input fields for this entity
        input_fields = set(data.keys())  # Get set of provided input fields

        if http_method == "PUT":
            # For PUT requests, validate that an integer ID is provided
            id = data.get("id")
            if not id or not isinstance(id, int):
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-0312",
                        "message": f"{http_method} request 'id' field error: The request data must have an integer 'id' field.",
                    }
                )  # Add error if ID is missing or not an integer
            acceptable_input_fields.add("id")  # Add id to acceptable fields for PUT requests

        # Checking if input fields are acceptable
        if not input_fields.issubset(acceptable_input_fields):
            # Validate that all provided fields are in the acceptable fields list
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-0313",
                    "message": f"'{http_method}' unacceptable input fields: the data input fields must be in {acceptable_input_fields}. the input contain {input_fields - acceptable_input_fields} fields that are not acceptable.",
                }
            )  # Add error with list of unacceptable fields

        # Call entity-specific validator function to perform detailed field validation
        errors = errors + entity_lookup_data[entity].get(INPUT_VALIDATOR_FUNCTION)(
            data=data, http_method=http_method
        )  # Add errors from entity-specific validator

    else:
        # Validate that HTTP method is one of the supported methods
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-0322",
                "message": f"Wrong HTTP MethodThe: The HTTP method must be one of POST, GET, PUT, and DELETE.",
            }
        )  # Add error if HTTP method is not supported
    return errors  # Return list of validation errors (empty if validation passed)


# POST/PUT input data validation functions
# NOTE: all validators change the choice fields to lower case if there is not any problem.
def mls_data_validator(data, http_method=None):
    """
    error codes start from 03141
    """
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)
    name = data.get("name")  # Extract name field from data
    short_name = data.get("short_name")  # Extract short_name field from data
    country_id = data.get("country_id")  # Extract country_id field from data
    status = data.get("status")  # Extract status field from data

    address = data.get("address")  # Extract address field from data
    contact_email = data.get("contact_email")  # Extract contact_email field from data
    website = data.get("website")  # Extract website field from data
    contact_details = data.get("contact_details")  # Extract contact_details field from data

    if data.get("contract_type"):
        data["contract_type"] = data.get("contract_type").lower()  # Normalize contract_type to lowercase
    contract_type = data.get("contract_type")  # Extract normalized contract_type field

    group_id = data.get("group_id")  # Extract group_id field from data
    report_required = data.get("report_required")  # Extract report_required field from data

    if data.get("report_interval"):
        data["report_interval"] = data.get("report_interval").lower()  # Normalize report_interval to lowercase
    report_interval = data.get("report_interval")  # Extract normalized report_interval field

    report_day_of_month = data.get("report_day_of_month")  # Extract report_day_of_month field from data

    if (is_post and (not name or not (isinstance(name, str) and len(name) <= 200))) or (
        not is_post and name and not (isinstance(name, str) and len(name) <= 200)
    ):
        # Validate name field: required for POST, must be string with max length 200
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03141",
                "message": f"{http_method} request 'name' field error: The request data must have a String 'name' field with 200 charachters length.",
            }
        )
    if (
        is_post
        and (
            not short_name
            or not (isinstance(short_name, str) and len(short_name) <= 32)
        )
    ) or (
        not is_post
        and short_name
        and not (isinstance(short_name, str) and len(short_name) <= 32)
    ):
        # Validate short_name field: required for POST, must be string with max length 32
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03142",
                "message": f"{http_method} request 'short_name' field error: The request data must have a String 'short_name' field with 60 charachters length.",
            }
        )
    if country_id and not isinstance(country_id, int):
        # Validate country_id field: if provided, must be an integer
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03143",
                "message": f"{http_method} request 'country_id' field error: The 'country_id' field must be number.",
            }
        )

    if (is_post and (status == None or not isinstance(status, bool))) or (
        not is_post and status != None and not isinstance(status, bool)
    ):
        # Validate status field: required for POST, must be boolean
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03145",
                "message": f"{http_method} request 'status' field error: The request data must have a Boolean 'status' field.",
            }
        )
    if group_id and not isinstance(group_id, int):
        # Validate group_id field: if provided, must be an integer
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03146",
                "message": f"{http_method} request 'group_id' field error: The 'group_id' field must be number.",
            }
        )
    if report_required:
        if not isinstance(report_required, bool):
            # Validate report_required field: if provided, must be boolean
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03147",
                    "message": f"{http_method} request 'report_required' field error: The request data must have a Boolean 'report_required' field.",
                }
            )

        if not report_interval or not report_interval in [
            "weekly",
            "monthly",
            "quarterly",
            "yearly",
        ]:
            # If reports are required, validate report_interval: must be one of the allowed values
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03148",
                    "message": f"'{http_method}' request 'report_interval' field error: The 'report_interval' field must be one of 'weekly', 'monthly', 'quarterly', 'yearly'.",
                }
            )
        if not report_day_of_month or not (
            isinstance(report_day_of_month, int) and report_day_of_month in range(1, 32)
        ):
            # If reports are required, validate report_day_of_month: must be integer between 1-31
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03149",
                    "message": f"{http_method} request 'report_day_of_month' field error: The request data must have a Integer 'report_day_of_month' field.",
                }
            )
    else:
        data["report_required"] = False  # If report_required is not provided, default to False

    contract_types = ["vendor", "consultant", "technical", "provider"]
    if contract_type and not contract_type in contract_types:
        # Validate contract_type field: if provided, must be one of the allowed values
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031410",
                "message": f"'{http_method}' request 'contract_type' field error: The 'contract_type' field must be one of {contract_types}.",
            }
        )

    # ========= contact information fields validation ===============
    if address and not (isinstance(address, str) and len(address) <= 256):
        # Validate address field: if provided, must be string with max length 256
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031411",
                "message": f"{http_method} request 'address' field error: The max length of 'address' field must be 256 charachters.",
            }
        )

    if contact_email and not (
        isinstance(contact_email, str) and len(contact_email) <= 256
    ):
        # Validate contact_email field: if provided, must be string with max length 256
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031412",
                "message": f"{http_method} request 'contact_email' field error: The max length of 'contact_email' field must be 256 charachters.",
            }
        )

    if website and not (isinstance(website, str) and len(website) <= 256):
        # Validate website field: if provided, must be string with max length 256
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031413",
                "message": f"{http_method} request 'website' field error: The max length of 'website' field must be 256 charachters.",
            }
        )

    if contact_details and not isinstance(contact_details, dict):
        # Validate contact_details field: if provided, must be a dictionary (JSON object)
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031414",
                "message": f"{http_method} request 'contact_details' field error: The 'contact_details' field must be a JSON.",
            }
        )

    return errors  # Return list of validation errors (empty if validation passed)


def mls_document_data_validator(data, http_method=None):
    """
    error codes start from 03151
    """
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)

    mls_id = data.get("mls_id")  # Extract MLS server ID from data
    name = data.get("name")  # Extract document name from data
    url = data.get("url")  # Extract document URL (S3 path) from data
    if data.get("document_type"):
        data["document_type"] = data.get("document_type").lower()  # Normalize document_type to lowercase
    document_type = data.get("document_type")  # Extract normalized document_type field

    if (is_post and (not name or not (isinstance(name, str) and len(name) <= 200))) or (
        not is_post and name and not (isinstance(name, str) and len(name) <= 200)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03151",
                "message": f"'{http_method}' request 'mls_access_credential' 'name' field error: The request data must have a String 'name' field with macimum 200 character length.",
            }
        )  # Validate name field: required for POST, must be string with max length 200

    if (
        is_post
        and (not document_type or not document_type in ["contract", "manual", "client"])
    ) or (
        not is_post
        and document_type
        and not document_type in ["contract", "manual", "client"]
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03152",
                "message": f"'{http_method}' request 'document_type' field error: The 'document_type' field must be one of 'basic' or 'digest'.", #### change the error message
            }
        )  # Validate document_type: must be one of the allowed values (note: error message is incorrect)

    if http_method.upper() == "POST":
        if not mls_id or not isinstance(mls_id, int):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03153",
                    "message": f"'{http_method}' request 'mls_id' field error: The request data must have an Integer 'mls_id' field.",
                }
            )  # For POST requests, validate mls_id is required and must be an integer

        if not url:
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03154",
                    "message": f"'{http_method}' request 'url' field error: The 'url' field must be an URL.",
                }
            )  # For POST requests, validate url is required
        else:
            file_type = url.split("/")[-1].split(".")[-1]  # Extract file extension from URL
            acceptable_file_type = ["pdf", "doc", "docx", "xls", "xlsx", "txt"]  # Define allowed file types
            if file_type.lower() not in acceptable_file_type:
                errors.append(
                    {
                        "code": f"{ERROR_CODE_PREFIX}-031541",
                        "message": f"'{http_method}' request 'url' file type error: The 'url' field file type must be in {acceptable_file_type}.",
                    }
                )  # Validate file extension is one of the allowed types

    elif http_method.upper() == "PUT":
        if mls_id:
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03155",
                    "message": f"'{http_method}' request 'mls_id' field error: After creating a document you cant change its 'mls_id'.",
                }
            )  # For PUT requests, validate mls_id cannot be changed after creation
        if url:
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03156",
                    "message": f"'{http_method}' request 'url' field error: After creating a document you cant change its 'url'",
                }
            )  # For PUT requests, validate url cannot be changed after creation

    return errors  # Return list of validation errors (empty if validation passed)


def mls_config_data_validator(data, http_method=None):
    """
    error codes start from 03161
    """
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)

    # Extract new fields from data
    mls_id = data.get("mls_id")  # Extract MLS server ID from data
    status = data.get("status")  # Extract status field from data
    filter_param = data.get("filter_param")  # Extract filter parameter from data
    path_param = data.get("path_param")  # Extract path parameter from data
    custom_prefix_key = data.get("custom_prefix_key")  # Extract custom prefix key from data
    custom_prefix_value = data.get("custom_prefix_value")  # Extract custom prefix value from data
    custom_mapping = data.get("custom_mapping")  # Extract custom mapping from data
    timezone = data.get("timezone")  # Extract timezone from data

    # Validate mls_id: required for POST, must be integer
    if (is_post and (not mls_id or not isinstance(mls_id, int))) or (
        not is_post and mls_id and not isinstance(mls_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03161",
                "message": f"{http_method} request 'mls_id' field error: The request data must have an Integer 'mls_id' field.",
            }
        )

    # Validate status: required for POST, must be boolean
    if (is_post and (status == None or not isinstance(status, bool))) or (
        not is_post and status != None and not isinstance(status, bool)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03162",
                "message": f"{http_method} request 'status' field error: The request data must have a Boolean 'status' field.",
            }
        )

    # Validate filter_param: optional, must be string with max length 255 if provided
    if filter_param and not (isinstance(filter_param, str) and len(filter_param) <= 255):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03163",
                "message": f"{http_method} request 'filter_param' field error: The 'filter_param' field must be a String with maximum 255 characters length.",
            }
        )

    # Validate path_param: optional, must be string with max length 255 if provided
    if path_param and not (isinstance(path_param, str) and len(path_param) <= 255):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03164",
                "message": f"{http_method} request 'path_param' field error: The 'path_param' field must be a String with maximum 255 characters length.",
            }
        )

    # Validate custom_prefix_key: optional, must be string with max length 255 if provided
    if custom_prefix_key and not (isinstance(custom_prefix_key, str) and len(custom_prefix_key) <= 255):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03165",
                "message": f"{http_method} request 'custom_prefix_key' field error: The 'custom_prefix_key' field must be a String with maximum 255 characters length.",
            }
        )

    # Validate custom_prefix_value: optional, must be string with max length 255 if provided
    if custom_prefix_value and not (isinstance(custom_prefix_value, str) and len(custom_prefix_value) <= 255):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03166",
                "message": f"{http_method} request 'custom_prefix_value' field error: The 'custom_prefix_value' field must be a String with maximum 255 characters length.",
            }
        )

    # Validate custom_mapping: optional, must be string (can be JSON text) if provided
    if custom_mapping and not isinstance(custom_mapping, str):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03167",
                "message": f"{http_method} request 'custom_mapping' field error: The 'custom_mapping' field must be a String (JSON text).",
            }
        )

    # Validate timezone: optional, must be string with max length 100 if provided
    if timezone and not (isinstance(timezone, str) and len(timezone) <= 100):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03168",
                "message": f"{http_method} request 'timezone' field error: The 'timezone' field must be a String with maximum 100 characters length.",
            }
        )

    return errors  # Return list of validation errors (empty if validation passed)


def mls_accesse_data_validator(data, http_method=None):
    """
    Error codes start from 03171"""
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)

    accounting_status = data.get("accounting_status")  # Extract accounting_status field from data
    if (
        is_post
        and (accounting_status == None or not isinstance(accounting_status, bool))
    ) or (
        not is_post
        and accounting_status != None
        and not isinstance(accounting_status, bool)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03171",
                "message": f"'{http_method}' request 'accounting_status' field error: The request data must have a Boolean 'accounting_status' field.",
            }
        )  # Validate accounting_status: required for POST, must be boolean

    connection_status = data.get("connection_status")  # Extract connection_status field from data
    if (
        is_post
        and (connection_status == None or not isinstance(connection_status, bool))
    ) or (
        not is_post
        and connection_status != None
        and not isinstance(connection_status, bool)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03172",
                "message": f"'{http_method}' request 'connection_status' field error: The request data must have a Boolean 'connection_status' field.",
            }
        )  # Validate connection_status: required for POST, must be boolean

    mls_id = data.get("mls_id")  # Extract MLS server ID from data
    mls_config_id = data.get("mls_config_id")  # Extract MLS configuration ID from data
    if (is_post and (not mls_id or not isinstance(mls_id, int))) or (
        not is_post and mls_id and not isinstance(mls_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03173",
                "message": f"'{http_method}' request 'mls_id' field error: The request data must have an integer 'mls_id' field.",
            }
        )  # Validate mls_id: required for POST, must be integer
    if mls_config_id and not isinstance(mls_config_id, int):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03174",
                "message": f"'{http_method}' request 'mls_config_id' field error: The request data must have an integer 'mls_config_id' field.",
            }
        )  # Validate mls_config_id: if provided, must be integer

    if data.get("contract_type"):
        data["contract_type"] = data.get("contract_type").lower()  # Normalize contract_type to lowercase
    contract_type = data.get("contract_type")  # Extract normalized contract_type field
    contract_types = ["vendor", "consultant", "technical provider"]  # Define allowed contract types
    if contract_type and not contract_type in contract_types:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03175",
                "message": f"'{http_method}' request 'contract_type' field error: The 'contract_type' field value must be one of {contract_types}.",
            }
        )  # Validate contract_type: if provided, must be one of the allowed values

    feed_type_connection_type_id = data.get("feed_type_connection_type_id")  # Extract feed type connection ID
    if (
        is_post
        and (
            not feed_type_connection_type_id
            or not isinstance(feed_type_connection_type_id, int)
        )
    ) or (
        not is_post
        and feed_type_connection_type_id
        and not isinstance(feed_type_connection_type_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03176",
                "message": f"'{http_method}' request 'feed_type_connection_type_id' field error: The request data must have an integer 'feed_type_connection_type_id' field.",
            }
        )  # Validate feed_type_connection_type_id: required for POST, must be integer

    platform_id = data.get("platform_id")  # Extract platform ID from data
    platform_credential_id = data.get("platform_credential_id")  # Extract platform credential ID from data
    if platform_id:
        if not isinstance(platform_id, int):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03177",
                    "message": f"'{http_method}' request 'platform_id' field error: The request data must have an integer 'platform_id' field.",
                }
            )  # Validate platform_id: if provided, must be integer
        if not platform_credential_id or not isinstance(platform_credential_id, int):
            errors.append(
                {
                    "code": f"{ERROR_CODE_PREFIX}-03178",
                    "message": f"'{http_method}' request 'platform_credential_id' field error: The request data must have an integer 'platform_credential_id' field.",
                }
            )  # If platform_id is provided, platform_credential_id is required and must be integer
    return errors  # Return list of validation errors (empty if validation passed)


def mls_accesse_credential_data_validator(data, http_method=None):
    """
    Error codes start from 03181"""
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)

    mls_access_id = data.get("mls_access_id")  # Extract MLS access ID from data
    class_name = data.get("class_name")  # Extract class name from data
    version = data.get("version")  # Extract version from data
    is_media = data.get("is_media")  # Extract is_media flag from data
    media_resource = data.get("media_resource")  # Extract media resource from data
    media_object_type = data.get("media_object_type")  # Extract media object type from data
    rets_user_name = data.get("rets_user_name")  # Extract RETS username from data
    rets_password = data.get("rets_password")  # Extract RETS password from data
    agent_user_name = data.get("agent_user_name")  # Extract agent username from data
    agent_password = data.get("agent_password")  # Extract agent password from data
    login_url = data.get("login_url")  # Extract login URL from data

    if data.get("auth_type"):
        data["auth_type"] = data.get("auth_type").lower()  # Normalize auth_type to lowercase
    auth_type = data.get("auth_type")  # Extract normalized auth_type field

    if data.get("request_method"):
        data["request_method"] = data.get("request_method").lower()  # Normalize request_method to lowercase
    request_method = data.get("request_method")  # Extract normalized request_method field

    options = data.get("options")  # Extract options field (JSON data)

    if (is_post and (not mls_access_id or not isinstance(mls_access_id, int))) or (
        not is_post and mls_access_id and not isinstance(mls_access_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03181",
                "message": f"'{http_method}' request 'mls_access_id' field error: The request data must have an integer 'mls_access_id' field.",
            }
        )  # Validate mls_access_id: required for POST, must be integer

    if (
        is_post
        and (
            not class_name
            or not (isinstance(class_name, str) and len(class_name) <= 200)
        )
    ) or (
        not is_post
        and class_name
        and not (isinstance(class_name, str) and len(class_name) <= 200)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03182",
                "message": f"'{http_method}' request 'mls_access_credential' 'class_name' field error: The request data must have a String 'class_name' field with maximum 200 charachters.",
            }
        )  # Validate class_name: required for POST, must be string with max length 200

    if (
        is_post
        and (not version or not (isinstance(version, str) and len(version) <= 20))
    ) or (
        not is_post
        and version
        and not (isinstance(version, str) and len(version) <= 20)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03183",
                "message": f"'{http_method}' request 'mls_access_credential' 'version' field error: The request data must have a String 'version' field with maximum 20 charachters.",
            }
        )  # Validate version: required for POST, must be string with max length 20

    has_media_resource_error = False  # Flag to track media resource validation errors
    has_media_object_type_error = False  # Flag to track media object type validation errors
    if "is_media" in data.keys() and not isinstance(is_media, bool):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03184",
                "message": f"'{http_method}' request 'is_media' field error: The request data must have a Boolean 'is_media' field.",
            }
        )  # Validate is_media: if provided, must be boolean
    elif is_post and "is_media" in data.keys() and is_media:
        # For POST requests with is_media=True, validate media_resource and media_object_type
        has_media_resource_error = not media_resource or not (
            isinstance(media_resource, str) and len(rets_user_name) <= 200
        )  # Note: using rets_user_name length instead of media_resource
        has_media_object_type_error = not media_object_type or not (
            isinstance(media_object_type, str) and len(rets_user_name) <= 200
        )  # Note: using rets_user_name length instead of media_object_type
    if http_method.upper() == "PUT":
        # For PUT requests, validate media_resource and media_object_type if provided
        has_media_resource_error = media_resource and not (
            isinstance(media_resource, str) and len(rets_user_name) <= 200
        )  # Note: using rets_user_name length instead of media_resource
        has_media_object_type_error = media_object_type and not (
            isinstance(media_object_type, str) and len(rets_user_name) <= 200
        )  # Note: using rets_user_name length instead of media_object_type
    if has_media_resource_error:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03185",
                "message": f"'{http_method}' request 'media_resource' field error: The request data must have a String 'media_resource' field with maximum 200 charachters.",
            }
        )  # Add error for invalid media_resource
    if has_media_object_type_error:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03186",
                "message": f"'{http_method}' request 'media_object_type' field error: The request data must have a String 'media_object_type' field with maximum 200 charachters.",
            }
        )  # Add error for invalid media_object_type
    if rets_user_name and not (
        isinstance(rets_user_name, str) and len(rets_user_name) <= 200
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03187",
                "message": f"'{http_method}' request 'rets_user_name' field error: The request data must have a String 'rets_user_name' field with maximum 200 charachters.",
            }
        )  # Validate rets_user_name: if provided, must be string with max length 200

    if rets_password and not (
        isinstance(rets_password, str) and len(rets_password) <= 256
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03188",
                "message": f"'{http_method}' request 'rets_password' field error: The request data must have a String 'rets_password' field with maximum 256 charachters.",
            }
        )  # Validate rets_password: if provided, must be string with max length 256

    if agent_user_name and not (
        isinstance(agent_user_name, str) and len(agent_user_name) <= 200
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03189",
                "message": f"'{http_method}' request 'agent_user_name' field error: The request data must have a String 'agent_user_name' field with maximum 200 charachters.",
            }
        )  # Validate agent_user_name: if provided, must be string with max length 200

    if agent_password and not (
        isinstance(agent_password, str) and len(agent_password) <= 256
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031810",
                "message": f"'{http_method}' request 'agent_password' field error: The request data must have a String 'agent_password' field with maximum 256 charachters.",
            }
        )  # Validate agent_password: if provided, must be string with max length 256

    if (is_post and (not login_url or not isinstance(login_url, str))) or (
        not is_post and login_url and not isinstance(login_url, str)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031811",
                "message": f"'{http_method}' request 'login_url' field error: The 'login_url' field must be String.",
            }
        )  # Validate login_url: required for POST, must be string

    if (is_post and (not auth_type or not auth_type in ["basic", "digest"])) or (
        not is_post and auth_type and not auth_type in ["basic", "digest"]
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031812",
                "message": f"'{http_method}' request 'auth_type' field error: The 'auth_type' field must be one of 'basic' or 'digest'.",
            }
        )  # Validate auth_type: required for POST, must be one of the allowed values

    if (is_post and (not request_method or not request_method in ["post", "get"])) or ( #### add put and delete
        not is_post and request_method and not request_method in ["post", "get"]
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031813",
                "message": f"'{http_method}' request 'request_method' field error: The 'request_method' field must be one of 'POST', 'GET', 'PUT', or 'DELETE'.",
            }
        )  # Validate request_method: required for POST, must be one of the allowed values (note: error message lists more methods than actually allowed)

    if options and not isinstance(options, dict):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-031814",
                "message": f"'{http_method}' request 'options' field error: The 'options' field must be JSON.",
            }
        )  # Validate options: if provided, must be a dictionary (JSON object)

    return errors  # Return list of validation errors (empty if validation passed)


def payment_method_data_validator(data, http_method=None):
    """
    Error codes start from 03191"""
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)

    mls_access_id = data.get("mls_access_id")  # Extract MLS access ID from data
    data_url = data.get("data_url")  # Extract data URL from data
    setup_fee = data.get("setup_fee")  # Extract setup fee amount from data
    reactivation_fee = data.get("reactivation_fee")  # Extract reactivation fee amount from data
    payment_details = data.get("payment_details")  # Extract payment details (JSON data) from data
    is_auto_billing = data.get("is_auto_billing")  # Extract auto-billing flag from data
    payment_username = data.get("payment_username")  # Extract payment username from data
    payment_password = data.get("payment_password")  # Extract payment password from data

    if (is_post and (not mls_access_id or not isinstance(mls_access_id, int))) or (
        not is_post and mls_access_id and not isinstance(mls_access_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03191",
                "message": "'{http_method}' request 'mls_access_id' field error: The request data must have an Integer 'mls_access_id' field.",
            }
        )  # Validate mls_access_id: required for POST, must be integer

    if is_post and not data_url:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03192",
                "message": f"'{http_method}' request 'data_url' field error: The 'data_url' field must be an URL.",
            }
        )  # Validate data_url: required for POST requests

    if setup_fee and not isinstance(setup_fee, int):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03193",
                "message": f"'{http_method}' request 'setup_fee' field error: The request data must have an Integer 'setup_fee' field.",
            }
        )  # Validate setup_fee: if provided, must be integer

    if reactivation_fee and not isinstance(reactivation_fee, int):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03194",
                "message": f"'{http_method}' request 'reactivation_fee' field error: The request data must have an Integer 'reactivation_fee' field.",
            }
        )  # Validate reactivation_fee: if provided, must be integer

    if payment_username and not (
        isinstance(payment_username, str) and len(payment_username) <= 200
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03195",
                "message": f"'{http_method}' request 'payment_username' field error: The request data must have a String 'payment_username' field with maximum 200 charachters.",
            }
        )  # Validate payment_username: if provided, must be string with max length 200

    if payment_password and not (
        isinstance(payment_password, str) and len(payment_password) <= 256
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03196",
                "message": f"'{http_method}' request 'payment_password' field error: The request data must have a String 'payment_password' field with maximum 256 charachters.",
            }
        )  # Validate payment_password: if provided, must be string with max length 256

    if payment_details and not isinstance(payment_details, dict):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03197",
                "message": f"'{http_method}' request 'payment_details' field error: The request data must have an JSON 'payment_details' field.",
            }
        )  # Validate payment_details: if provided, must be a dictionary (JSON object)
    if (
        is_post and (is_auto_billing == None or not isinstance(is_auto_billing, bool))
    ) or (
        not is_post
        and is_auto_billing != None
        and not isinstance(is_auto_billing, bool)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03198",
                "message": f"'{http_method}' request 'is_auto_billing' field error: The request data must have a Boolean 'is_auto_billing' field.",
            }
        )  # Validate is_auto_billing: required for POST, must be boolean

    return errors  # Return list of validation errors (empty if validation passed)


def flat_rate_item_data_validator(data, http_method=None):
    """
    Error codes start from 03201"""
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)

    mls_access_payment_method_id = data.get("mls_access_payment_method_id")  # Extract payment method ID from data
    fee = data.get("fee")  # Extract fee amount from data
    if data.get("interval"):
        data["interval"] = data.get("interval").lower()  # Normalize interval value to lowercase for consistent validation
    interval = data.get("interval")  # Extract normalized interval field
    start_date = data.get("start_date")  # Extract start date for the rate item
    payment_day_of_month = data.get("payment_day_of_month")  # Extract day of month when payment should be processed
    if data.get("target"):
        data["target"] = data.get("target").lower()  # Normalize target value to lowercase for consistent validation
    target = data.get("target")  # Extract normalized target field

    if (
        is_post
        and (
            not mls_access_payment_method_id
            or not isinstance(mls_access_payment_method_id, int)
        )
    ) or (
        not is_post
        and mls_access_payment_method_id
        and not isinstance(mls_access_payment_method_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03201",
                "message": f"'{http_method}' request 'mls_access_payment_method_id' field error: The request data must have an Integer 'mls_access_payment_method_id' field.",
            }
        )  # Validate payment method ID: required for POST, must be integer

    if (is_post and (not fee or not isinstance(fee, int))) or (
        not is_post and fee and not isinstance(fee, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03202",
                "message": f"'{http_method}' request 'fee' field error: The request data must have an Integer 'fee' field.",
            }
        )  # Validate fee: required for POST, must be integer

    intervals = ["yearly", "quarterly", "monthly", "one-time"]  # Define allowed billing interval values
    if (is_post and (not interval or interval.lower() not in intervals)) or (
        not is_post and interval and interval.lower() not in intervals
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03203",
                "message": f"'{http_method}' request 'interval' field error: The 'interval' field must be one of {intervals}.",
            }
        )  # Validate interval: required for POST, must be one of the allowed values

    if is_post and not start_date:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03204",
                "message": f"'{http_method}' request 'start_date' field error: The 'start_date' field must be a datetime String like '2022-09-01T00:00:00.000000' .",
            }
        )  # Validate start_date: required for POST, must be a datetime string

    if (
        is_post
        and (
            not payment_day_of_month
            or not (
                isinstance(payment_day_of_month, int)
                and payment_day_of_month in range(1, 32)
            )
        )
    ) or (
        not is_post
        and payment_day_of_month
        and not (
            isinstance(payment_day_of_month, int)
            and payment_day_of_month in range(1, 32)
        )
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03205",
                "message": f"'{http_method}' request 'payment_day_of_month' field error: The request data must have an Integer 'payment_day_of_month' field between 1 and 31.",
            }
        )  # Validate payment_day_of_month: required for POST, must be integer between 1-31

    targets = ["mls", "platform", "other"]  # Define allowed target values for the payment
    if (is_post and (not target or target not in targets)) or (
        not is_post and target and target not in targets
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03206",
                "message": f"'{http_method}' request 'target' field error: The 'target' field must be one of {targets}.",
            }
        )  # Validate target: required for POST, must be one of the allowed values
    return errors  # Return list of validation errors (empty if validation passed)


def range_rate_item_data_validator(data, http_method=None):
    """
    Error codes start from 03211
    """
    errors = []  # Initialize empty list to collect validation errors
    is_post = http_method.upper() == "POST"  # Flag to check if this is a POST request (create) vs PUT (update)

    mls_access_payment_method_id = data.get("mls_access_payment_method_id")  # Extract payment method ID from data
    fee = data.get("fee")  # Extract fee amount from data

    if data.get("interval"):
        data["interval"] = data.get("interval").lower()  # Normalize interval value to lowercase for consistent validation
    interval = data.get("interval")  # Extract normalized interval field

    start_date = data.get("start_date")  # Extract start date for the rate item
    payment_day_of_month = data.get("payment_day_of_month")  # Extract day of month when payment should be processed

    if data.get("target"):
        data["target"] = data.get("target").lower()  # Normalize target value to lowercase for consistent validation
    target = data.get("target")  # Extract normalized target field

    if data.get("contract_type"):
        data["contract_type"] = data.get("contract_type").lower()  # Normalize contract_type value to lowercase
    contract_type = data.get("contract_type")  # Extract normalized contract_type field

    from_number = data.get("from_number")  # Extract lower bound of the range
    to_number = data.get("to_number")  # Extract upper bound of the range

    if data.get("per_type"):
        data["per_type"] = data.get("per_type").lower()  # Normalize per_type value to lowercase
    per_type = data.get("per_type")  # Extract normalized per_type field

    if (
        is_post
        and (
            not mls_access_payment_method_id
            or not isinstance(mls_access_payment_method_id, int)
        )
    ) or (
        not is_post
        and mls_access_payment_method_id
        and not isinstance(mls_access_payment_method_id, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03211",
                "message": f"{http_method}' request 'mls_access_payment_method_id' field error: The request data must have an Integer 'mls_access_payment_method_id' field.",
            }
        )  # Validate payment method ID: required for POST, must be integer
    if (is_post and (not fee or not isinstance(fee, int))) or (
        not is_post and fee and not isinstance(fee, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03212",
                "message": f"{http_method}' request 'fee' field error: The request data must have an Integer 'fee' field.",
            }
        )  # Validate fee: required for POST, must be integer

    if (
        is_post
        and (
            not interval
            or interval not in ["yearly", "quarterly", "monthly", "one-time"]
        )
    ) or (
        not is_post
        and interval
        and interval not in ["yearly", "quarterly", "monthly", "one-time"]
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03213",
                "message": f"{http_method}' request 'interval' field error: The 'is_auto_billing' field must be one of 'yearly','quarterly','monthly','one-time'.",
            }
        )  # Validate interval: required for POST, must be one of the allowed values (note: error message incorrectly references 'is_auto_billing')

    if is_post and not start_date:
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03214",
                "message": f"{http_method}' request 'start_date' field error: The 'start_date' field must be a datetime String with format like '2022-09-01T00:00:00.000000' .",
            }
        )  # Validate start_date: required for POST, must be a datetime string

    if (
        is_post
        and (
            not payment_day_of_month
            or not (
                isinstance(payment_day_of_month, int)
                and payment_day_of_month in range(1, 32)
            )
        )
    ) or (
        not is_post
        and payment_day_of_month
        and not (
            isinstance(payment_day_of_month, int)
            and payment_day_of_month in range(1, 32)
        )
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03215",
                "message": f"{http_method}' request 'payment_day_of_month' field error: The request data must have an Integer 'payment_day_of_month' field.",
            }
        )  # Validate payment_day_of_month: required for POST, must be integer between 1-31

    if (is_post and (not target or target not in ["mls", "platform", "other"])) or (
        not is_post and target and target not in ["mls", "platform", "other"]
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03216",
                "message": f"{http_method}' request 'target' field error: The 'target' field must be one of 'MLS', 'Platform', 'Other'.",
            }
        )  # Validate target: required for POST, must be one of the allowed values (note: error message uses capitalized values)

    if (
        is_post
        and (
            not contract_type
            or contract_type
            not in [
                "per agent fee",
                "per brokerage fee",
                "per user fee",
            ]
        )
    ) or (
        not is_post
        and contract_type
        and contract_type
        not in [
            "per agent fee",
            "per brokerage fee",
            "per user fee",
        ]
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03217",
                "message": f"{http_method}' request 'contract_type' field error: The 'contract_type' field must be one of 'Per Agent Fee', 'Per Brokerage Fee', 'Per User Fee'.",
            }
        )  # Validate contract_type: required for POST, must be one of the allowed values (note: error message uses capitalized values)

    if (is_post and (not from_number or not isinstance(from_number, int))) or (
        not is_post and from_number and not isinstance(from_number, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03218",
                "message": f"{http_method}' request 'from_number' field error: The request data must have an Integer 'from_number' field.",
            }
        )  # Validate from_number: required for POST, must be integer (lower bound of range)

    if (is_post and (not to_number or not isinstance(to_number, int))) or (
        not is_post and to_number and not isinstance(to_number, int)
    ):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-03219",
                "message": f"{http_method}' request 'to_number' field error: The request data must have an Integer 'to_number' field.",
            }
        )  # Validate to_number: required for POST, must be integer (upper bound of range)

    if (
        is_post and (not per_type or per_type not in ["per client fee", "flat fee"])
    ) or (not is_post and per_type and per_type not in ["per client fee", "flat fee"]):
        errors.append(
            {
                "code": f"{ERROR_CODE_PREFIX}-032110",
                "message": f"{http_method}' request 'per_type' field error: The 'per_type' field must be one of 'Per Client Fee', 'Flat Fee'.",
            }
        )  # Validate per_type: required for POST, must be one of the allowed values (note: error message uses capitalized values)
    return errors  # Return list of validation errors (empty if validation passed)
