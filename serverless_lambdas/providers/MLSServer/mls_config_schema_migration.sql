-- MLS Config Schema Migration Script
-- This script migrates the mls_configs table from the old schema to the new schema
-- 
-- IMPORTANT: This script should be run in a transaction to ensure atomicity
-- IMPORTANT: Backup your database before running this migration
-- IMPORTANT: This migration will result in data loss for the removed columns

BEGIN;

-- Step 1: Remove old columns that are no longer needed
-- Note: This will permanently delete data in these columns
ALTER TABLE mls_configs DROP COLUMN IF EXISTS name;
ALTER TABLE mls_configs DROP COLUMN IF EXISTS resource;
ALTER TABLE mls_configs DROP COLUMN IF EXISTS query;
ALTER TABLE mls_configs DROP COLUMN IF EXISTS limit;
ALTER TABLE mls_configs DROP COLUMN IF EXISTS unique_field;
ALTER TABLE mls_configs DROP COLUMN IF EXISTS update_interval;
ALTER TABLE mls_configs DROP COLUMN IF EXISTS photo_time_stamp_field;

-- Step 2: Add new columns with appropriate data types and constraints
ALTER TABLE mls_configs ADD COLUMN filter_param VARCHAR(255) NULL;
ALTER TABLE mls_configs ADD COLUMN path_param VARCHAR(255) NULL;
ALTER TABLE mls_configs ADD COLUMN custom_prefix_key VARCHAR(255) NULL;
ALTER TABLE mls_configs ADD COLUMN custom_prefix_value VARCHAR(255) NULL;
ALTER TABLE mls_configs ADD COLUMN custom_mapping TEXT NULL;
ALTER TABLE mls_configs ADD COLUMN timezone VARCHAR(100) NULL;

-- Step 3: Create indexes for frequently filtered fields to improve query performance
CREATE INDEX IF NOT EXISTS idx_mls_configs_filter_param ON mls_configs(filter_param);
CREATE INDEX IF NOT EXISTS idx_mls_configs_path_param ON mls_configs(path_param);
CREATE INDEX IF NOT EXISTS idx_mls_configs_custom_prefix_key ON mls_configs(custom_prefix_key);
CREATE INDEX IF NOT EXISTS idx_mls_configs_timezone ON mls_configs(timezone);

-- Step 4: Add comments to document the new columns
COMMENT ON COLUMN mls_configs.filter_param IS 'Filter parameter for data queries (nullable)';
COMMENT ON COLUMN mls_configs.path_param IS 'Path parameter for API endpoints (nullable)';
COMMENT ON COLUMN mls_configs.custom_prefix_key IS 'Custom prefix key for data mapping (nullable)';
COMMENT ON COLUMN mls_configs.custom_prefix_value IS 'Custom prefix value for data mapping (nullable)';
COMMENT ON COLUMN mls_configs.custom_mapping IS 'Custom field mapping configuration as JSON text (nullable)';
COMMENT ON COLUMN mls_configs.timezone IS 'Timezone for data processing (nullable)';

-- Commit the transaction
COMMIT;

-- Verification queries (run these after the migration to verify the changes)
-- SELECT column_name, data_type, is_nullable, character_maximum_length 
-- FROM information_schema.columns 
-- WHERE table_name = 'mls_configs' 
-- ORDER BY ordinal_position;

-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'mls_configs';
