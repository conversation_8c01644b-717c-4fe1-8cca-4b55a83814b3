from abc import ABC, abstractmethod  # Import abstract base class for creating abstract classes
from datetime import datetime  # For handling date and time operations
import json  # For JSON serialization/deserialization
import logging  # For logging messages and errors
from typing import List, Tuple  # Type hints for better code documentation
import psycopg2  # PostgreSQL database adapter for Python

from realtyfeed.datasets import sample_response  # Default response template for API responses
from realtyfeed.exceptions import (  # Custom exception classes for error handling
    BaseRealtyfeedException,  # Base exception class for all Realtyfeed exceptions
    RealtyfeedException,  # General exception for Realtyfeed errors
    DataInvalidException,  # Exception for invalid data inputs
    DatabaseException,  # Exception for database operation errors
)
from realtyfeed.orm import (  # Database ORM utilities
    RDSHandler,  # Base handler for RDS database operations
    GetRecordException,  # Exception for record retrieval errors
    CreateRecordException,  # Exception for record creation errors
    UpdateRecordException,  # Exception for record update errors
    DeleteRecordException,  # Exception for record deletion errors
)
from realtyfeed.permission import Permission  # Permission checking utilities
from database import GroupMlsDocumentRDS  # Custom RDS handler for MLS documents
from dynamodb import DynamoDbHandler  # Handler for DynamoDB operations
from validations import (  # Data validation utilities and models
    validate_data,  # General data validation function
    mls_data_validator,  # Validator for MLS server data
    mls_document_data_validator,  # Validator for MLS document data
    mls_config_data_validator,  # Validator for MLS configuration data
    mls_accesse_data_validator,  # Validator for MLS access data
    mls_accesse_credential_data_validator,  # Validator for MLS access credentials
    payment_method_data_validator,  # Validator for payment method data
    flat_rate_item_data_validator,  # Validator for flat rate payment items
    range_rate_item_data_validator,  # Validator for range rate payment items
    GroupMLSDocumentQueryParams,  # Pydantic model for GET request query parameters
    GroupMLSDocumentCreateBody,  # Pydantic model for POST request body
    GroupMLSDocumentUpdateBody,  # Pydantic model for PUT request body
    GroupMLSDocumentDELETEBody,  # Pydantic model for DELETE request body
)
from s3 import change_s3, delete_file_from_s3, S3ActionException  # S3 file operations

##### should change entity_lookup_data only in this file to new fields
"""
Main Lambda handler for MLS Server API endpoints.

This module implements the AWS Lambda handler for processing API Gateway requests related to MLS servers.
It provides CRUD (Create, Read, Update, Delete) operations for various MLS-related entities including:
- MLS servers
- MLS documents
- MLS configurations
- MLS access credentials
- Payment methods

The module uses a processor pattern with specialized classes for handling different HTTP methods
(GET, POST, PUT, DELETE) for the group-mls-documents entity, and a more generic approach for other entities.

Key components:
- Entity lookup data dictionary defining validation and database mapping for each entity type
- GroupMLSRequestDocumentBaseProcessor and its subclasses for handling group-mls-documents requests
- lambda_handler: Main entry point for AWS Lambda
- Error handling with detailed error codes and messages
"""


logger = logging.getLogger()  # Initialize logger for this module


# entities
MLS = "mls_server"  # Constant for MLS server entity type
MLS_ACCESS = "mls_access"  # Constant for MLS access entity type
MLS_ACCESS_CREDENTIAL = "mls_access_credential"  # Constant for MLS access credential entity type
MLS_DOCUMENT = "mls_document"  # Constant for MLS document entity type
MLS_CONFIG = "mls_config"  # Constant for MLS configuration entity type
MLS_ACCESS_PAYMENT_METHOD = "mls_access_payment_method"  # Constant for payment method entity type
MLS_ACCESS_PAYMENT_METHOD_FLAT_RATE_ITEM = "mls_access_payment_method_flat_rate_item"  # Constant for flat rate payment item
MLS_ACCESS_PAYMENT_METHOD_RANGE_RATE_ITEM = "mls_access_payment_method_range_rate_item"  # Constant for range rate payment item

# lookup data dict keys
TABLE = "table_key"  # Key for database table name in entity_lookup_data
INPUT_VALIDATOR_FUNCTION = "input_validator_function_holder_key"  # Key for validation function
ACCEPTABLE_INPUT_FIELDS = "acceptable_input_fields_key"  # Key for allowed input fields
ACCEPTABLE_SELECTED_FIELDS = "acceptable_selected_fields_key"  # Key for fields to select from database
LIST_FILTER_ACCEPTABLE_FIELDS_KEY = "list_filter_acceptable_fields_key"  # Key for fields that can be used in filters
JOIN_STATEMENT_KEY = "join_statement_key"  # Key for SQL JOIN statements
GROUP_BY = "group_by_key"  # Key for SQL GROUP BY clause

# Error prefix code
ERROR_CODE_PREFIX = "prv-05-01"  # Prefix for error codes in this module
NOT_FOUND_RECORD_ERROR_CODE = f"{ERROR_CODE_PREFIX}-001"  # Error code for record not found

ACCESS_MLS_VENDOR_DATA = "access_mls_vendor_data"  # Permission name for accessing MLS vendor data
SUB_PERMISSION_READ = "read"  # Sub-permission for read-only access
SUB_PERMISSION_READ_WRITE = "read/write"  # Sub-permission for read-write access


# The lambda looks up entities data from this variable.
entity_lookup_data = {
    MLS: {  # Configuration for MLS server entity
        TABLE: "mls_servers",  # Database table name
        INPUT_VALIDATOR_FUNCTION: mls_data_validator,  # Function to validate input data
        ACCEPTABLE_INPUT_FIELDS: {  # Fields that can be provided in requests
            "group_id",  # ID of the group that owns this MLS server
            "name",  # Name of the MLS server
            "short_name",  # Short name/abbreviation for the MLS server
            "country_id",  # Country where the MLS server operates
            "image",  # Logo or image for the MLS server
            "status",  # Status of the MLS server (active, inactive, etc.)
            "contract_type",  # Type of contract with the MLS server
            "report_required",  # Whether reports are required
            "report_interval",  # How often reports should be generated
            "report_day_of_month",  # Day of month for report generation
            "last_listings_update",  # When listings were last updated
            "address",  # Physical address of the MLS server
            "contact_email",  # Contact email for the MLS server
            "website",  # Website URL for the MLS server
            "contact_details",  # Additional contact information
            "details",  # Miscellaneous details about the MLS server
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields that can be used for filtering
            "id",  # Primary key
            "group_id",  # Group that owns the MLS server
            "name",  # Name of the MLS server
            "short_name",  # Short name/abbreviation
            "country_id",  # Country where the MLS server operates
            "status",  # Status (active, inactive, etc.)
            "contract_type",  # Type of contract
            "report_required",  # Whether reports are required
            "report_interval",  # How often reports should be generated
            "report_day_of_month",  # Day of month for report generation
            "last_listings_update",  # When listings were last updated
            "creation_date",  # When the record was created
            "modification_date",  # When the record was last modified
            "address",  # Physical address
            "contact_email",  # Contact email
            "website",  # Website URL
        ],
        ACCEPTABLE_SELECTED_FIELDS: """  # SQL SELECT clause defining the fields to retrieve for MLS servers
            mls_servers.*,  # Select all columns from the mls_servers table (primary entity data)
            groups.name as group_name,  # Include the name of the associated group with an alias for clarity
            json_agg(  # Use PostgreSQL's json_agg to collect related feed types into a JSON array
                DISTINCT  # Ensure only unique feed types are included in the aggregation
                    feed_types_connection_types.*  # Include all columns from the feed_types_connection_types table
            ) as feed_types  # Alias the aggregated JSON array as 'feed_types' in the result set
        """,
        JOIN_STATEMENT_KEY: """  # SQL JOIN clauses to connect MLS servers with related entities
            LEFT JOIN groups                        on groups.id = mls_servers.group_id  # Connect to groups table to get group information
            LEFT JOIN mls_accesses                  on mls_accesses.mls_id = mls_servers.id  # Connect to accesses to find all access records for this MLS
            LEFT JOIN feed_types_connection_types   on feed_types_connection_types.id = mls_accesses.feed_type_connection_type_id  # Connect to feed types through the access records
            """,
        GROUP_BY: " mls_servers.id, groups.name",  # GROUP BY clause required for aggregation functions
                                                  # Groups by MLS server ID and group name to maintain data integrity when using json_agg
    },
    MLS_DOCUMENT: {  # Configuration for MLS document entity
        TABLE: "mls_documents",  # Database table name
        INPUT_VALIDATOR_FUNCTION: mls_document_data_validator,  # Validation function
        ACCEPTABLE_INPUT_FIELDS: {"name", "url", "document_type", "mls_id"},  # Allowed input fields
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields for filtering
            "id",  # Primary key
            "name",  # Document name
            "document_type",  # Type of document (contract, manual, etc.)
            "mls_id",  # ID of the associated MLS server
            "creation_date",  # When the document was created
            "modification_date",  # When the document was last modified
        ],
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Select all fields
    },
    ########  should change the keys into desired keys and delete unwanted ones
    MLS_CONFIG: {  # Configuration for MLS configuration entity
        TABLE: "mls_configs",  # Database table name
        INPUT_VALIDATOR_FUNCTION: mls_config_data_validator,  # Validation function
        ACCEPTABLE_INPUT_FIELDS: {  # Allowed input fields
            "mls_id",  # ID of the associated MLS server
            "name",  # Configuration name
            "resource",  # Resource to fetch (e.g., Property, Agent)
            "query",  # Query parameters for the resource
            "limit",  # Maximum number of records to fetch
            "unique_field",  # Field that uniquely identifies records
            "status",  # Status of the configuration
            "update_interval",  # How often to update data
            "photo_time_stamp_field",  # Field for photo timestamps
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields for filtering
            "id",  # Primary key        
            "mls_id",  # Associated MLS server
            "name",  # Configuration name
            "resource",  # Resource to fetch
            "limit",  # Maximum records
            "unique_field",  # Unique identifier field
            "status",  # Status
            "update_interval",  # Update frequency
            "photo_time_stamp_field",  # Photo timestamp field
            "creation_date",  # Creation timestamp
            "modification_date",  # Last modification timestamp
        ],
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Select all fields
    },
    ###### this part is related to dropdown feature
    MLS_ACCESS: {  # Configuration for MLS access entity
        TABLE: "mls_accesses",  # Database table name
        INPUT_VALIDATOR_FUNCTION: mls_accesse_data_validator,  # Validation function
        ACCEPTABLE_INPUT_FIELDS: {  # Allowed input fields
            "accounting_status",  # Status of accounting for this access
            "connection_status",  # Status of the connection
            "mls_id",  # Associated MLS server
            "contract_type",  # Type of contract
            "feed_type_connection_type_id",  # Type of feed and connection
            "mls_config_id",  # Associated MLS configuration
            #### these 2 below
            "platform_id",  # Platform used for access
            "platform_credential_id",  # Credentials for the platform
            "limitation",  # Any limitations on the access
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields for filtering
            "id",  # Primary key
            "accounting_status",  # Accounting status
            "connection_status",  # Connection status
            "mls_id",  # Associated MLS server
            "contract_type",  # Contract type
            "feed_type_connection_type_id",  # Feed and connection type
            "mls_config_id",  # Associated configuration
            "platform_id",  # Platform
            "platform_credential_id",  # Platform credentials
            "limitation",  # Limitations
            "creation_date",  # Creation timestamp
            "modification_date",  # Last modification timestamp
        ],
        ACCEPTABLE_SELECTED_FIELDS: " mls_accesses.*, platforms.name as platform_name ",  # Select fields with join
        JOIN_STATEMENT_KEY: " LEFT JOIN platforms on platforms.id = mls_accesses.platform_id ",  # Join with platforms table
    },
    MLS_ACCESS_CREDENTIAL: {  # Configuration for MLS access credential entity
        TABLE: "mls_access_credentials",  # Database table name
        INPUT_VALIDATOR_FUNCTION: mls_accesse_credential_data_validator,  # Validation function
        ACCEPTABLE_INPUT_FIELDS: {  # Allowed input fields
            "mls_access_id",  # Associated MLS access
            "class_name",  # Class name for the credential
            "version",  # Version of the API
            "is_media",  # Whether this is for media access
            "media_resource",  # Resource for media
            "media_object_type",  # Type of media object
            "rets_user_name",  # RETS username
            "rets_password",  # RETS password
            "agent_user_name",  # Agent username
            "agent_password",  # Agent password
            "login_url",  # URL for login
            "auth_type",  # Authentication type
            "request_method",  # HTTP method for requests
            "options",  # Additional options
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields for filtering
            "id",  # Primary key
            "mls_access_id",  # Associated MLS access
            "class_name",  # Class name
            "version",  # API version
            "is_media",  # Media access flag
            "media_resource",  # Media resource
            "media_object_type",  # Media object type
            "rets_user_name",  # RETS username
            "agent_user_name",  # Agent username
            "auth_type",  # Authentication type
            "request_method",  # Request method
            "creation_date",  # Creation timestamp
            "modification_date",  # Last modification timestamp
        ],
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Select all fields
    },
    MLS_ACCESS_PAYMENT_METHOD: {  # Configuration for payment method entity
        TABLE: "mls_access_payment_methods",  # Database table name
        INPUT_VALIDATOR_FUNCTION: payment_method_data_validator,  # Validation function
        ACCEPTABLE_INPUT_FIELDS: {  # Allowed input fields
            "mls_access_id",  # Associated MLS access
            "data_url",  # URL for payment data
            "setup_fee",  # One-time setup fee
            "reactivation_fee",  # Fee for reactivation
            "lambda_arn",  # ARN for Lambda function
            "payment_url",  # URL for payments
            "payment_username",  # Username for payment system
            "payment_password",  # Password for payment system
            "payment_details",  # Additional payment details
            "is_auto_billing",  # Whether billing is automatic
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields for filtering
            "id",  # Primary key
            "mls_access_id",  # Associated MLS access
            "setup_fee",  # Setup fee
            "reactivation_fee",  # Reactivation fee
            "lambda_arn",  # Lambda ARN
            "payment_username",  # Payment username
            "is_auto_billing",  # Auto-billing flag
            "creation_date",  # Creation timestamp
            "modification_date",  # Last modification timestamp
        ],
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Select all fields
    },
    MLS_ACCESS_PAYMENT_METHOD_FLAT_RATE_ITEM: {  # Configuration for flat rate payment item
        TABLE: "mls_access_payment_method_flat_rate_items",  # Database table name
        INPUT_VALIDATOR_FUNCTION: flat_rate_item_data_validator,  # Validation function
        ACCEPTABLE_INPUT_FIELDS: {  # Allowed input fields
            "mls_access_payment_method_id",  # Associated payment method
            "fee",  # Fee amount
            "interval",  # Billing interval
            "start_date",  # Start date for billing
            "payment_day_of_month",  # Day of month for payment
            "target",  # Target for the payment
            "description",  # Description of the payment
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields for filtering
            "id",  # Primary key
            "mls_access_payment_method_id",  # Associated payment method
            "fee",  # Fee amount
            "interval",  # Billing interval
            "start_date",  # Start date
            "payment_day_of_month",  # Payment day
            "target",  # Target
            "description",  # Description
            "creation_date",  # Creation timestamp
            "modification_date",  # Last modification timestamp
        ],
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Select all fields
    },
    MLS_ACCESS_PAYMENT_METHOD_RANGE_RATE_ITEM: {  # Configuration for range rate payment item
        TABLE: "mls_access_payment_method_range_rate_items",  # Database table name
        INPUT_VALIDATOR_FUNCTION: range_rate_item_data_validator,  # Validation function
        ACCEPTABLE_INPUT_FIELDS: {  # Allowed input fields
            "mls_access_payment_method_id",  # Associated payment method
            "fee",  # Fee amount
            "interval",  # Billing interval
            "start_date",  # Start date for billing
            "payment_day_of_month",  # Day of month for payment
            "target",  # Target for the payment
            "description",  # Description of the payment
            "contract_type",  # Type of contract
            "from_number",  # Starting number for range
            "to_number",  # Ending number for range
            "per_type",  # Type of per-unit calculation
        },
        LIST_FILTER_ACCEPTABLE_FIELDS_KEY: [  # Fields for filtering
            "id",  # Primary key
            "mls_access_payment_method_id",  # Associated payment method
            "fee",  # Fee amount
            "interval",  # Billing interval
            "start_date",  # Start date
            "payment_day_of_month",  # Payment day
            "target",  # Target
            "description",  # Description
            "contract_type",  # Contract type
            "from_number",  # Range start
            "to_number",  # Range end
            "per_type",  # Per-unit type
            "creation_date",  # Creation timestamp
            "modification_date",  # Last modification timestamp
        ],
        ACCEPTABLE_SELECTED_FIELDS: "*",  # Select all fields
    },
}


class GroupMLSRequestDocumentBaseProcessor(ABC):
    """Abstract base class for processing MLS document requests."""
    
    def __init__(self, rds: GroupMlsDocumentRDS, http_method: str, user_id: int):
        self.rds = rds  # Database connection handler for MLS documents
        self.http_method = http_method  # HTTP method (GET, POST, PUT, DELETE)
        self.user_id = user_id  # ID of the authenticated user making the request

    @abstractmethod
    def process(self, response: dict, **kwargs) -> dict:
        """Abstract method that must be implemented by subclasses to process specific HTTP methods.
        
        Args:
            response: The response dictionary to populate
            **kwargs: Additional arguments specific to each HTTP method
            
        Returns:
            The populated response dictionary
        """
        return

    def _check_permission(
        self, mls_vendor_group_id: int, sub_permissions: List[str], *args, **kwargs
    ):
        """Verify that the user has the required permissions for the MLS vendor group.
        
        Args:
            mls_vendor_group_id: ID of the MLS vendor group to check permissions for
            sub_permissions: List of required sub-permissions (read, read/write)
            
        Raises:
            PermissionDeniedException: If the user doesn't have the required permissions
        """
        permission = Permission(
            user_id=self.user_id,
            group_id=mls_vendor_group_id,
            group_permission=ACCESS_MLS_VENDOR_DATA,
        )
        # Check if user has any of the specified sub-permissions
        # Will raise an exception if permission is denied
        permission.has_permission(*sub_permissions)
        return

    def _fetch_mls_document(self, mls_doc_id: int) -> dict | None:
        """Retrieve an MLS document by ID with all related information.
        
        Args:
            mls_doc_id: ID of the MLS document to retrieve
            
        Returns:
            The MLS document as a dictionary, or None if not found or invalid
        """
        mls_doc = self.rds.get_detailed_mls_document_by_id(mls_doc_id)
        if not mls_doc or not mls_doc.get("mls_vendor_group_id"):
            return None
        return mls_doc


class GroupMLSDocumentGETProcessor(GroupMLSRequestDocumentBaseProcessor):
    """Processor for handling GET requests for MLS documents."""

    def process(self, response: dict, query_params: dict) -> dict:
        """Process a GET request for MLS documents.
        
        Args:
            response: The response dictionary to populate
            query_params: Query parameters from the request
            
        Returns:
            The populated response with MLS documents
        """
        # Validate and parse query parameters using Pydantic model
        qp_obj = self._get_validated_query_params(query_params)
        # Check if user has permission to read MLS documents for this vendor group
        self._check_permission(
            mls_vendor_group_id=qp_obj.mls_vendor_group_id,
            sub_permissions=[SUB_PERMISSION_READ, SUB_PERMISSION_READ_WRITE],
        )
        # Fetch documents from database with pagination
        result, total_count = self._fetch_mls_documents(qp_obj)
        # Populate response with results
        response["result_count"] = total_count
        response["result"] = result
        response["is_success"] = True
        return response

    def _get_validated_query_params(
        self, query_params: dict
    ) -> GroupMLSDocumentQueryParams:
        """Convert raw query parameters to a validated Pydantic model.
        
        Args:
            query_params: Raw query parameters from the request
            
        Returns:
            Validated GroupMLSDocumentQueryParams object
        """
        return GroupMLSDocumentQueryParams(**query_params)

    def _fetch_mls_documents(
        self, qp_obj: GroupMLSDocumentQueryParams
    ) -> Tuple[List[dict], int]:
        """Retrieve MLS documents from the database based on query parameters.
        
        Args:
            qp_obj: Validated query parameters object
            
        Returns:
            Tuple of (list of documents, total count) for pagination
        """
        # Call database method to get documents with filtering, sorting, and pagination
        result, total_count = self.rds.get_mls_documents(
            filters=qp_obj.model_dump(
                include=qp_obj.query_filter_fields, exclude_unset=True
            ),  # Extract filter fields from query params
            order_by=f"{qp_obj.orderby_field} {qp_obj.orderby_direction}",  # Build ORDER BY clause
            limit=qp_obj.limit,  # Number of records to return
            offset=qp_obj.offset,  # Number of records to skip
        )
        return result, total_count


class GroupMLSDocumentPOSTProcessor(GroupMLSRequestDocumentBaseProcessor):
    """Processor for handling POST requests to create new MLS documents."""

    def process(self, response: dict, request_data: dict) -> dict:
        """Process a POST request to create a new MLS document.
        
        Args:
            response: The response dictionary to populate
            request_data: Request body data
            
        Returns:
            The populated response with the created MLS document
        """
        # Validate request data using Pydantic model
        validated_data = self._validate_request_data(request_data)

        # Check if user has permission to create MLS documents for this vendor group
        self._check_permission(
            mls_vendor_group_id=validated_data["mls_vendor_group_id"],
            sub_permissions=[SUB_PERMISSION_READ_WRITE],
        )

        # Get the MLS server ID for the vendor group
        mls_server_id = self._fetch_mls_server_id(validated_data["mls_vendor_group_id"])

        # Create document record in database with empty URL (will be updated after S3 upload)
        mls_document = self._create_mls_document_in_database(
            document_name=validated_data["name"],
            document_url="",  # Empty URL initially
            document_type=validated_data.get("document_type", "contract"),  # Default to contract type
            mls_server_id=mls_server_id,
        )
        
        # Construct S3 path for the document
        new_path = f"mls_servers/{mls_server_id}/documents/{mls_document['id']}"
        # Move document from temporary S3 location to permanent location
        new_file_url = change_s3(new_path=new_path, file_path=request_data.get("url"))
        # Update document record with the new URL
        mls_document = self._update_mls_document_url_in_database(
            mls_document["id"], new_file_url
        )
        
        # Populate response with the created document
        response["result"] = mls_document
        response["is_success"] = True
        return response

    def _validate_request_data(self, request_data: dict) -> dict:
        """Validate request data using Pydantic model.
        
        Args:
            request_data: Raw request data
            
        Returns:
            Validated data as a dictionary
        """
        return GroupMLSDocumentCreateBody(**request_data).model_dump()

    def _fetch_mls_server_id(self, mls_vendor_group_id: int) -> int:
        """Get the MLS server ID associated with a vendor group.
        
        Args:
            mls_vendor_group_id: ID of the MLS vendor group
            
        Returns:
            ID of the associated MLS server
        """
        mls_server = self.rds.get_mls_server_by_mls_vendor_group_id(mls_vendor_group_id)
        return int(mls_server["id"])

    def _create_mls_document_in_database(
        self,
        document_name: str,
        document_url: str,
        document_type: str,
        mls_server_id: int,
    ) -> dict:
        """Create a new MLS document record in the database.
        
        Args:
            document_name: Name of the document
            document_url: URL of the document in S3
            document_type: Type of document (contract, manual, etc.)
            mls_server_id: ID of the associated MLS server
            
        Returns:
            The created document record
        """
        return self.rds.create_mls_document(
            name=document_name,
            url=document_url,
            document_type=document_type,
            mls_id=mls_server_id,
        )

    def _update_mls_document_url_in_database(self, id: int, document_url: str) -> dict:
        """Update the URL of an existing MLS document record.
        
        Args:
            id: ID of the document to update
            document_url: New URL for the document
            
        Returns:
            The updated document record
        """
        return self.rds.update_mls_document_url(id, document_url)


class GroupMLSDocumentPUTProcessor(GroupMLSRequestDocumentBaseProcessor):
    """Processor for handling PUT requests to update existing MLS documents."""

    def process(self, response: dict, request_data: dict) -> dict:
        """Process a PUT request to update an existing MLS document.
        
        Args:
            response: The response dictionary to populate
            request_data: Request body data
            
        Returns:
            The populated response with the updated MLS document
        """
        # Validate request data using Pydantic model
        validated_data = self._validate_request_data(request_data)
        # Fetch existing document to check permissions and get current values
        existing_mls_doc = self._fetch_mls_document(validated_data["id"])
        if not existing_mls_doc:
            # Raise exception if document not found or invalid
            raise RealtyfeedException(
                error_message=f"MLS document with ID={validated_data['id']} not found or is invalid"
            )

        # Check if user has permission to update MLS documents for this vendor group
        self._check_permission(
            mls_vendor_group_id=existing_mls_doc["mls_vendor_group_id"],
            sub_permissions=[SUB_PERMISSION_READ_WRITE],
        )

        # Prepare updated document data by merging existing data with changes
        updated_mls_doc_data = self._prepare_updated_mls_document_data(
            validated_data, existing_mls_doc
        )
        # Update document in database
        self._update_document_in_database(updated_mls_doc_data)

        # Fetch updated document and return in response
        response["result"] = self._fetch_mls_document(updated_mls_doc_data["id"])
        response["is_success"] = True
        return response

    def _validate_request_data(self, body: dict) -> dict:
        """Validate request data using Pydantic model.
        
        Args:
            body: Raw request body data
            
        Returns:
            Validated data as a dictionary
        """
        # Use exclude_unset=True to only include fields that were explicitly set in the request
        return GroupMLSDocumentUpdateBody(**body).model_dump(exclude_unset=True)

    def _prepare_updated_mls_document_data(
        self, request_data: dict, existing_mls_doc: dict
    ) -> dict:
        """Merge existing document data with changes from request.
        
        Args:
            request_data: Validated request data with changes
            existing_mls_doc: Existing document data from database
            
        Returns:
            Merged document data with changes applied
        """
        # Start with a copy of the existing document
        updated_mls_doc = existing_mls_doc.copy()
        # Apply changes from request data
        for key, value in request_data.items():
            if key in updated_mls_doc:
                updated_mls_doc[key] = value

        # If a new document is uploaded, move it from temp to permanent S3 location
        if path := request_data.get("url"):
            # Construct S3 path for the document
            new_path = (
                f"{MLS}s/{updated_mls_doc['mls_id']}/documents/{updated_mls_doc['id']}"
            )
            # Move document and update URL
            updated_mls_doc["url"] = change_s3(new_path=new_path, file_path=path)

        return updated_mls_doc

    def _update_document_in_database(self, updated_mls_doc_data: dict) -> None:
        """Update document record in database with new values.
        
        Args:
            updated_mls_doc_data: Document data with changes applied
        """
        self.rds.update_mls_document(
            id=updated_mls_doc_data["id"],
            name=updated_mls_doc_data["name"],
            url=updated_mls_doc_data["url"],
            document_type=updated_mls_doc_data["document_type"],
        )


class GroupMLSDocumentDELETEProcessor(GroupMLSRequestDocumentBaseProcessor):
    """Processor for handling DELETE requests to remove MLS documents."""

    def process(self, response: dict, request_data: dict) -> dict:
        """Process a DELETE request to remove an MLS document.
        
        Args:
            response: The response dictionary to populate
            request_data: Request body data containing the document ID
            
        Returns:
            The populated response with deletion confirmation
        """
        # Validate the request data using Pydantic model to ensure it contains a valid ID
        validated_data = self._validate_request_data(request_data)
        
        # Fetch the document to be deleted to check permissions and confirm it exists
        mls_doc = self._fetch_mls_document(validated_data["id"])
        if not mls_doc:
            # If document not found, raise an exception with appropriate error message
            raise RealtyfeedException(
                error_message=f"MLS document with ID={validated_data['id']} not found or is invalid"
            )

        # Check if the user has permission to delete documents for this vendor group
        self._check_permission(
            mls_vendor_group_id=mls_doc["mls_vendor_group_id"],
            sub_permissions=[SUB_PERMISSION_READ_WRITE],  # Only users with write permission can delete
        )

        # Delete the document record from the database
        self._delete_mls_document_from_database(mls_doc["id"])
        
        # Delete the document file from S3 storage
        self._delete_mls_document_from_s3(mls_doc.get("url"))

        # Populate the response with success message
        response["result"] = (
            f"MLS request document with ID={mls_doc['id']} deleted successfully."
        )
        response["is_success"] = True
        return response

    def _validate_request_data(self, body: dict) -> dict:
        """Validate request data using Pydantic model.
        
        Args:
            body: Raw request body data
            
        Returns:
            Validated data as a dictionary
        """
        # Use GroupMLSDocumentDELETEBody Pydantic model to validate the request body
        return GroupMLSDocumentDELETEBody(**body).model_dump()

    def _delete_mls_document_from_database(self, mls_doc_id: int) -> None:
        """Delete an MLS document record from the database.
        
        Args:
            mls_doc_id: ID of the document to delete
        """
        # Call the RDS handler method to delete the document by ID
        self.rds.delete_mls_document_by_id(mls_doc_id)

    def _delete_mls_document_from_s3(self, mls_doc_url: str) -> None:
        """Delete an MLS document file from S3 storage.
        
        Args:
            mls_doc_url: URL of the document in S3
        """
        # Call the S3 utility function to delete the file from S3 bucket
        delete_file_from_s3(mls_doc_url)


def handle_group_mls_documents(response: dict, event: dict) -> dict:
    """Handle group-mls-documents entity requests (for MLS vendor).
    
    This function routes requests to the appropriate processor based on HTTP method.
    
    Args:
        response: The response dictionary to populate
        event: The Lambda event containing request details
        
    Returns:
        The populated response with operation results
    """
    # Extract user ID from the authorizer context in the event
    user_id = int(eval(event["authorizer_context"]["user_data"])["user_id"])
    
    # Extract HTTP method and request parameters
    http_method = event["method"]
    query_params = event["queryParams"]
    body = event["body"]

    # Use context manager to ensure database connection is properly closed
    with GroupMlsDocumentRDS() as rds:
        # Route request to appropriate processor based on HTTP method
        if http_method == "GET":
            # For GET requests, use the GET processor to retrieve documents
            processor = GroupMLSDocumentGETProcessor(rds, http_method, user_id)
            response = processor.process(response, query_params)
        elif http_method == "POST":
            # For POST requests, use the POST processor to create new documents
            processor = GroupMLSDocumentPOSTProcessor(rds, http_method, user_id)
            response = processor.process(response, body)
        elif http_method == "PUT":
            # For PUT requests, use the PUT processor to update existing documents
            processor = GroupMLSDocumentPUTProcessor(rds, http_method, user_id)
            response = processor.process(response, body)
        elif http_method == "DELETE":
            # For DELETE requests, use the DELETE processor to remove documents
            processor = GroupMLSDocumentDELETEProcessor(rds, http_method, user_id)
            response = processor.process(response, body)
        else:
            # If HTTP method is not supported, raise an exception
            raise RealtyfeedException(
                error_message=f"{http_method} http method is not allowed"
            )

    # Return the populated response
    return response


def get_lasted_updated_date_from_dynamo(mls_ids: list):
    """Retrieve the last update timestamps for MLS servers from DynamoDB.
    
    Args:
        mls_ids: List of MLS server IDs to retrieve timestamps for
        
    Returns:
        Dictionary mapping MLS IDs to their last update timestamps
    """
    # Create a DynamoDB handler to interact with the database
    dynamodb_handler = DynamoDbHandler()
    
    # Initialize dictionary to store results
    mls_last_updates = {}

    # Process each MLS ID in the list
    for mls_id in mls_ids:
        # Get MLS server info from DynamoDB
        mls_info = dynamodb_handler.get_mls_server_info(mls_id)
        if mls_info:
            # Extract last_updated field from the info
            last_updated = mls_info.get("last_updated")
            if isinstance(last_updated, str):
                # If last_updated is a string, use it directly
                mls_last_updates[mls_id] = last_updated
            elif isinstance(last_updated, dict):
                # If last_updated is a dictionary (multiple timestamps),
                # find the most recent timestamp
                max_date = max(
                    last_updated.values(), key=lambda x: datetime.fromisoformat(x)
                )
                mls_last_updates[mls_id] = max_date
        else:
            # If no info found, set timestamp to None
            mls_last_updates[mls_id] = None

    # Return the dictionary of MLS IDs to timestamps
    return mls_last_updates


def lambda_handler(event, context):
    try:
        # Log the incoming event for debugging purposes
        logger.info(f"event: {event}")
        # Create a copy of the sample response template to populate
        response = sample_response.copy()
        # Extract the entity type from the event (determines which table/resource to operate on)
        entity = event.get("entity", None)

        # Special handling for group-mls-documents entity which uses a different processing pattern
        if entity == "group-mls-documents":
            # Process using the specialized handler and return serialized response
            return json.loads(
                json.dumps(handle_group_mls_documents(response, event), default=str)
            )

        try:
            # Extract request data from the event
            event_body = event.get("body", event)
            query_params = event.get("queryParams", {})
            http_method = event.get("method", "GET")
            # Commented out user_id extraction - likely handled elsewhere now
            # user_id = int(user_data["authorizer_context"]["user_id"])
        except Exception as e:
            # Log and return error if request data extraction fails
            logger.exception(f"ERROR | The request input data have some problem. e:{e}")
            response["error"] = f"The request input data have some problem."
            response["message_code"] = f"{ERROR_CODE_PREFIX}-01"
            return response

        # Extract the actual data payload based on HTTP method
        data = (
            json.loads(query_params.get("data"))
            if http_method == "GET"
            else event_body.get("data")
        )

        # Validate input data against schema and business rules
        errors = validate_data(entity_lookup_data, entity, data, http_method)
        if errors:
            # If validation fails, log errors and return error response
            logger.error(f"ERROR | validation errors |errors= {errors}")
            response["message_code"] = f"{ERROR_CODE_PREFIX}-03"
            response["error"] = errors
            return response

        # Initialize database connection handler for the entity's table
        try:
            rds_handler = RDSHandler(
                table_name=entity_lookup_data[entity][TABLE], is_cursor_dict=True #### table name is the same for mls_configs
            )
        except DatabaseException as e:
            # Log and raise exception if database connection fails
            logger.exception(f"ERROR | Database connection error. | {e}")
            response["message_code"] = f"{ERROR_CODE_PREFIX}-04"
            raise Exception("Database connection error.")

        # Initialize result container
        result = []
        
        # Handle GET requests - retrieve records
        if http_method == "GET":
            # Extract ID and filter parameters
            id = data.get("id")
            list_filter = data.get("list_filter")

            if id:
                # If ID is provided, fetch a single record by ID
                result = rds_handler.get_record(
                    id=id,
                    # Use entity configuration to determine which fields to select
                    select_fields=entity_lookup_data[entity].get(
                        ACCEPTABLE_SELECTED_FIELDS, "*"  ##### it takes all acceptable selected fields for each entity
                    ),
                    # Include any JOIN clauses defined for this entity
                    join=entity_lookup_data[entity].get(JOIN_STATEMENT_KEY, ""),
                    # Include any GROUP BY clauses defined for this entity
                    group_by=entity_lookup_data[entity].get(GROUP_BY, ""),
                )

                # For MLS servers, fetch last update timestamp from DynamoDB
                last_updated_data = get_lasted_updated_date_from_dynamo(
                    [result.get("id")]
                )

                # Add last update timestamp to the result if available
                single_last_sync_date = last_updated_data.get(result["id"])
                if single_last_sync_date is not None:
                    result["last_listings_update"] = single_last_sync_date

            elif list_filter:
                try:
                    # Extract filter fields and values from the request
                    acceptable_fields = entity_lookup_data[entity][
                        LIST_FILTER_ACCEPTABLE_FIELDS_KEY
                    ]
                    filters = list_filter.get("filters")
                    # Generate SQL WHERE condition based on filters
                    sql_condition = rds_handler.generate_sql_condition_by_filter(
                        conditions=filters, acceptable_fields=acceptable_fields
                    )
                except DataInvalidException as e:
                    # Handle invalid filter fields with appropriate error
                    logger.exception(f"ERROR | DataInvalidException | traceback: {e}")
                    response["message_code"] = f"{ERROR_CODE_PREFIX}-03"
                    response["error"] = [
                        {
                            "code": f"{ERROR_CODE_PREFIX}-03091",
                            "message": f"Acceptable fields for using in filters are {entity_lookup_data[entity][LIST_FILTER_ACCEPTABLE_FIELDS_KEY]} fields.",
                        }
                    ]
                    return response

                # First query to get total count of matching records
                result_count = rds_handler.get_record(
                    where=sql_condition["sql_where"],
                    placeholders_values_object=sql_condition["plceholders"],
                    group_by=entity_lookup_data[entity].get(GROUP_BY, ""),
                    select_fields=f"{entity_lookup_data[entity][TABLE]}.id",
                    join=entity_lookup_data[entity].get(JOIN_STATEMENT_KEY, ""),
                )
                
                # Second query to get actual records with pagination and sorting
                result = rds_handler.get_record(
                    where=sql_condition["sql_where"],
                    placeholders_values_object=sql_condition["plceholders"],
                    group_by=entity_lookup_data[entity].get(GROUP_BY, ""),
                    order_by=list_filter.get("order_by", ""),
                    limit=list_filter.get("limit", ""),
                    offset=list_filter.get("offset", ""),
                    select_fields=entity_lookup_data[entity].get(
                        ACCEPTABLE_SELECTED_FIELDS, "*"
                    ),
                    join=entity_lookup_data[entity].get(JOIN_STATEMENT_KEY, ""),
                )
                
                # For MLS servers, add last update timestamps from DynamoDB
                if entity == MLS:
                    if isinstance(result, list):
                        # For multiple results, fetch timestamps for all IDs
                        id_list = [obj["id"] for obj in result]
                        last_updated_data = get_lasted_updated_date_from_dynamo(id_list)
                        for final_data in result:
                            last_sync_date = last_updated_data.get(final_data["id"])
                            if last_sync_date is not None:
                                final_data["last_listings_update"] = last_sync_date
                    elif isinstance(result, dict):
                        # For single result, fetch timestamp for just that ID
                        id_list = [result["id"]]
                        last_updated_data = get_lasted_updated_date_from_dynamo(id_list)
                        last_sync_date = last_updated_data.get(result["id"])
                        if last_sync_date is not None:
                            result["last_listings_update"] = last_sync_date

                # Add total count to response for pagination
                response["result_count"] = len(result_count)

        # Handle POST requests - create new records
        elif http_method == "POST":
            # Add creation and modification timestamps
            now = datetime.now().isoformat()
            data["creation_date"] = now
            data["modification_date"] = now

            # Create record in database
            result = rds_handler.create_record(
                # Enable JSON column handling for fields like 'extra'
                convetr_json_columns=True,
                data_object=data,
            )

            # Special handling for MLS servers and documents with file attachments
            if (
                entity == MLS
                and data.get("image")
                or entity == MLS_DOCUMENT
                and data.get("url")
            ):
                # Determine file path and field based on entity type
                if entity == MLS:
                    path = data.get("image")
                    new_path = f"{MLS}s/{result['id']}/images"
                    file_field_key = "image"
                elif entity == MLS_DOCUMENT:
                    path = data.get("url")
                    new_path = f"{MLS}s/{result['mls_id']}/documents/{result['id']}"
                    file_field_key = "url"

                # Move file from temporary S3 location to permanent location
                new_fiel_url = change_s3(new_path=new_path, file_path=path)

                # Update record with permanent file URL
                result = rds_handler.update_record(
                    data_object={"id": result["id"], file_field_key: new_fiel_url},
                    returning="*",
                )

        # Handle PUT requests - update existing records
        elif http_method == "PUT":
            # Update modification timestamp
            data["modification_date"] = datetime.now().isoformat()

            # Special handling for MLS servers and documents with file attachments
            if (
                entity == MLS
                and data.get("image")
                or entity == MLS_DOCUMENT
                and data.get("url")
            ):
                # Extract record ID
                id = data.get("id")
                
                # Determine file path and field based on entity type
                if entity == MLS:
                    path = data.get("image")
                    new_path = f"{MLS}s/{data['id']}/images"
                    file_field_key = "image"
                elif entity == MLS_DOCUMENT:
                    path = data.get("url")
                    new_path = f"{MLS}s/{data['mls_id']}/documents/{data['id']}"
                    file_field_key = "url"
                
                # Move new file to permanent S3 location and update URL in data
                data[file_field_key] = change_s3(new_path=new_path, file_path=path)

                # Fetch existing record to get current file URL
                entity_record = rds_handler.get_record(id=data["id"])
                # Delete previous file from S3
                delete_file_from_s3(entity_record.get("image"))

            # Update record in database
            result = rds_handler.update_record(
                id=data.get("id"),
                data_object=data,
                convetr_json_columns=True,
                returning="*",
            )

        # Handle DELETE requests - remove records
        elif http_method == "DELETE":
            # Delete record from database
            result = rds_handler.delete_record(id=data.get("id"), returning="*")

            # For MLS servers and documents, also delete associated files
            if (
                entity == MLS
                and result.get("image")
                or entity == MLS_DOCUMENT
                and result.get("url")
            ):
                # Determine file path based on entity type
                if entity == MLS:
                    path = result.get("image")
                elif entity == MLS_DOCUMENT:
                    path = result.get("url")

                # Delete file from S3
                delete_file_from_s3(path)

        # Commit database changes
        rds_handler.commit_changes()
        # Set success flag and add result to response
        response["is_success"] = True
        response["result"] = result

    # Handle record not found exception
    except psycopg2.errors.NoDataFound as e:
        # Log the error but don't treat as failure - return empty result
        logger.exception(f"ERROR | Record Not Found | {e}")
        response["message_code"] = NOT_FOUND_RECORD_ERROR_CODE
        response["error"] = (
            f"ERROR | Record Not Found | the record that you want not found."
        )
        response["is_success"] = True
        response["result"] = []

    # Handle S3 operation failures
    except S3ActionException as e:
        logger.exception(f"ERROR | {e.__class__.__name__} | {e}")
        response["message_code"] = f"{ERROR_CODE_PREFIX}-06"
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while doing AWS-S3 action."
        )

    # Handle database read operation failures
    except GetRecordException as e:
        logger.exception(f"ERROR | {e.__class__.__name__} | {e}")
        response["message_code"] = f"{ERROR_CODE_PREFIX}-07"
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while getting record/records."
        )

    # Handle database create operation failures
    except CreateRecordException as e:
        logger.exception(f"ERROR | {e.__class__.__name__} | {e}")
        response["message_code"] = f"{ERROR_CODE_PREFIX}-08"
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while creating record."
        )

    # Handle database update operation failures
    except UpdateRecordException as e:
        logger.exception(f"ERROR | {e.__class__.__name__} | {e}")
        response["message_code"] = f"{ERROR_CODE_PREFIX}-09"
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while updating record."
        )

    # Handle database delete operation failures
    except DeleteRecordException as e:
        logger.exception(f"ERROR | {e.__class__.__name__} | {e}")
        response["message_code"] = f"{ERROR_CODE_PREFIX}-10"
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An error occurred while deleting record."
        )

    # Handle application-specific exceptions
    except BaseRealtyfeedException as e:
        # Log the failed event and prepare API-friendly error response
        logger.info(f"failed event: {event}")
        api_exception_obj = {
            "statusCode": e.http_status_code,
            "error": f"Error Message: {e.error_message}",
        }
        e.error_message = json.dumps(api_exception_obj)
        raise

    # Handle all other unexpected exceptions
    except Exception as e:
        logger.exception(f"ERROR | {e.__class__.__name__} | {e}")
        if response["message_code"] == "":
            response["message_code"] = f"{ERROR_CODE_PREFIX}-11"
        response["error"] = (
            f"ERROR | {e.__class__.__name__} | An unexpected error occurred."
        )

    # Ensure database connection is closed even if an exception occurred
    if ("rds_handler" in locals() or "rds_handler" in globals()) and rds_handler.conn:
        rds_handler.close_connection()

    # Return the final response
    return response
